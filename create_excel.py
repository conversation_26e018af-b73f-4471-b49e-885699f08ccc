import json
from datetime import datetime
import os

def create_excel_file():
    """
    创建Excel文件，以SN为主要标识，每个测试项作为一列
    """
    
    # 读取JSON数据
    print("正在读取JSON数据...")
    with open('Result_56.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"总共读取了 {len(data)} 条记录")
    
    # 按SN分组数据
    sn_data = {}
    test_items = set()
    
    for record in data:
        sn = record['sn']
        test_name = record['name']
        test_items.add(test_name)
        
        if sn not in sn_data:
            sn_data[sn] = {
                'sn': sn,
                'testid': record['testid'],
                'first_time': record['testtime'],
                'last_time': record['testtime'],
                'tests': {}
            }
        
        # 更新时间范围
        if record['testtime'] < sn_data[sn]['first_time']:
            sn_data[sn]['first_time'] = record['testtime']
        if record['testtime'] > sn_data[sn]['last_time']:
            sn_data[sn]['last_time'] = record['testtime']
        
        # 保存测试结果（如果有重复测试，保留最新的）
        sn_data[sn]['tests'][test_name] = {
            'val': record['val'],
            'min_val': record['RefMinVal'],
            'max_val': record['RefMaxVal'],
            'time': record['testtime']
        }
    
    test_items = sorted(list(test_items))
    print(f"发现 {len(sn_data)} 个唯一设备")
    print(f"发现 {len(test_items)} 种测试项目")
    
    # 创建Excel内容
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f'设备测试数据_{timestamp}.xls'
    
    print(f"正在生成Excel文件: {excel_filename}")
    
    # 创建HTML表格格式的Excel文件
    html_content = """
    <html xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:x="urn:schemas-microsoft-com:office:excel"
          xmlns="http://www.w3.org/TR/REC-html40">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="ProgId" content="Excel.Sheet">
        <meta name="Generator" content="Microsoft Excel 15">
        <!--[if gte mso 9]>
        <xml>
            <x:ExcelWorkbook>
                <x:ExcelWorksheets>
                    <x:ExcelWorksheet>
                        <x:Name>设备测试汇总</x:Name>
                        <x:WorksheetSource HRef="sheet001.htm"/>
                    </x:ExcelWorksheet>
                </x:ExcelWorksheets>
            </x:ExcelWorkbook>
        </xml>
        <![endif]-->
        <style>
            .header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; }
            .pass { background-color: #C6EFCE; }
            .fail { background-color: #FFC7CE; }
            .center { text-align: center; }
            table { border-collapse: collapse; width: 100%; }
            td, th { border: 1px solid #000000; padding: 5px; }
        </style>
    </head>
    <body>
        <table>
            <tr class="header">
                <th>序列号(SN)</th>
                <th>测试ID</th>
                <th>首次测试时间</th>
                <th>最后测试时间</th>
    """
    
    # 添加测试项目列头
    for test_name in test_items:
        html_content += f'<th>{test_name}_测试值</th>'
        html_content += f'<th>{test_name}_参考范围</th>'
        html_content += f'<th>{test_name}_合格状态</th>'
    
    html_content += "</tr>\n"
    
    # 添加数据行
    for sn in sorted(sn_data.keys()):
        device = sn_data[sn]
        html_content += f"""
            <tr>
                <td>{device['sn']}</td>
                <td>{device['testid']}</td>
                <td>{device['first_time']}</td>
                <td>{device['last_time']}</td>
        """
        
        # 添加每个测试项目的数据
        for test_name in test_items:
            if test_name in device['tests']:
                test_result = device['tests'][test_name]
                val = float(test_result['val'])
                min_val = float(test_result['min_val'])
                max_val = float(test_result['max_val'])
                is_pass = min_val <= val <= max_val
                
                status_class = 'pass' if is_pass else 'fail'
                status_text = '合格' if is_pass else '不合格'
                
                html_content += f'<td class="center">{test_result["val"]}</td>'
                html_content += f'<td class="center">{test_result["min_val"]}-{test_result["max_val"]}</td>'
                html_content += f'<td class="center {status_class}">{status_text}</td>'
            else:
                html_content += '<td class="center">-</td>'
                html_content += '<td class="center">-</td>'
                html_content += '<td class="center">未测试</td>'
        
        html_content += "</tr>\n"
    
    html_content += """
        </table>
    </body>
    </html>
    """
    
    # 写入Excel文件
    with open(excel_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"Excel文件已生成: {excel_filename}")
    print(f"文件包含 {len(sn_data)} 个设备的测试数据")
    
    # 生成统计信息
    print("\n=== 数据统计 ===")
    print(f"总设备数量: {len(sn_data)}")
    print(f"总测试记录数: {len(data)}")
    print(f"测试项目数量: {len(test_items)}")
    
    # 计算合格率
    for test_name in test_items:
        total_count = 0
        pass_count = 0
        values = []
        
        for device in sn_data.values():
            if test_name in device['tests']:
                total_count += 1
                test_result = device['tests'][test_name]
                val = float(test_result['val'])
                min_val = float(test_result['min_val'])
                max_val = float(test_result['max_val'])
                values.append(val)
                
                if min_val <= val <= max_val:
                    pass_count += 1
        
        if total_count > 0:
            pass_rate = (pass_count / total_count) * 100
            avg_val = sum(values) / len(values)
            print(f"{test_name}: 合格率 {pass_rate:.1f}% ({pass_count}/{total_count}), 平均值 {avg_val:.1f}")
    
    return excel_filename

if __name__ == "__main__":
    try:
        output_file = create_excel_file()
        print(f"\n✅ 处理完成！Excel文件: {output_file}")
        print("📝 该文件可以直接用Excel打开，包含完整的设备测试数据汇总表")
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
