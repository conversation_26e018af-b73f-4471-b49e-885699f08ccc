import json
import csv
from datetime import datetime
from collections import defaultdict

def process_test_data():
    """
    处理测试数据，以SN为主要标识整理数据并输出CSV文件
    """

    # 读取JSON数据
    print("正在读取JSON数据...")
    with open('Result_56.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"总共读取了 {len(data)} 条记录")

    # 获取所有唯一的SN和测试项目
    unique_sns = set()
    test_items = set()
    sn_data = defaultdict(list)

    for record in data:
        sn = record['sn']
        unique_sns.add(sn)
        test_items.add(record['name'])
        sn_data[sn].append(record)

    unique_sns = sorted(list(unique_sns))
    test_items = sorted(list(test_items))

    print(f"发现 {len(unique_sns)} 个唯一的设备序列号")
    print(f"发现 {len(test_items)} 种测试项目:")
    for item in test_items:
        print(f"  - {item}")

    # 创建汇总数据
    summary_data = []

    for sn in unique_sns:
        records = sn_data[sn]

        # 按时间排序
        records.sort(key=lambda x: x['testtime'])

        # 基本信息
        row = {
            'SN': sn,
            'TestID': records[0]['testid'],
            'FirstTestTime': records[0]['testtime'],
            'LastTestTime': records[-1]['testtime'],
            'TestCount': len(records)
        }

        # 为每个测试项目添加列
        test_results = {}
        for record in records:
            test_name = record['name']
            test_results[test_name] = record  # 保留最后一次测试结果

        for test_name in test_items:
            if test_name in test_results:
                record = test_results[test_name]
                row[f'{test_name}_值'] = record['val']
                row[f'{test_name}_最小值'] = record['RefMinVal']
                row[f'{test_name}_最大值'] = record['RefMaxVal']

                # 判断是否合格
                val = float(record['val'])
                min_val = float(record['RefMinVal'])
                max_val = float(record['RefMaxVal'])
                row[f'{test_name}_合格'] = '合格' if min_val <= val <= max_val else '不合格'
            else:
                row[f'{test_name}_值'] = ''
                row[f'{test_name}_最小值'] = ''
                row[f'{test_name}_最大值'] = ''
                row[f'{test_name}_合格'] = '未测试'

        summary_data.append(row)

    # 输出汇总数据到CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f'设备测试汇总_{timestamp}.csv'

    print(f"正在生成汇总CSV文件: {summary_file}")

    if summary_data:
        with open(summary_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=summary_data[0].keys())
            writer.writeheader()
            writer.writerows(summary_data)

    # 输出详细数据到CSV
    detail_file = f'详细测试数据_{timestamp}.csv'
    print(f"正在生成详细CSV文件: {detail_file}")

    # 按SN和时间排序
    sorted_data = sorted(data, key=lambda x: (x['sn'], x['testtime']))

    with open(detail_file, 'w', newline='', encoding='utf-8-sig') as f:
        if sorted_data:
            writer = csv.DictWriter(f, fieldnames=sorted_data[0].keys())
            writer.writeheader()
            writer.writerows(sorted_data)

    # 生成统计信息
    stats_file = f'统计信息_{timestamp}.csv'
    print(f"正在生成统计信息文件: {stats_file}")

    # 计算统计信息
    all_times = [record['testtime'] for record in data]
    min_time = min(all_times)
    max_time = max(all_times)

    stats_data = [
        {'统计项目': '总设备数量', '数值': len(unique_sns)},
        {'统计项目': '总测试记录数', '数值': len(data)},
        {'统计项目': '测试项目数量', '数值': len(test_items)},
        {'统计项目': '测试时间范围', '数值': f"{min_time} 至 {max_time}"},
        {'统计项目': '平均每设备测试次数', '数值': f"{len(data) / len(unique_sns):.1f}"}
    ]

    with open(stats_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=['统计项目', '数值'])
        writer.writeheader()
        writer.writerows(stats_data)

    # 为每个测试项目生成单独的统计文件
    for test_name in test_items:
        test_records = [record for record in data if record['name'] == test_name]
        if test_records:
            safe_name = test_name.replace('/', '_').replace('\\', '_').replace('(', '').replace(')', '')
            test_file = f'{safe_name}_{timestamp}.csv'
            print(f"正在生成测试项目文件: {test_file}")

            # 添加合格判断
            for record in test_records:
                val = float(record['val'])
                min_val = float(record['RefMinVal'])
                max_val = float(record['RefMaxVal'])
                record['合格状态'] = '合格' if min_val <= val <= max_val else '不合格'

            with open(test_file, 'w', newline='', encoding='utf-8-sig') as f:
                if test_records:
                    fieldnames = list(test_records[0].keys())
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(test_records)

    print(f"\n处理完成！生成的文件:")
    print(f"  - {summary_file}: 以SN为主键的设备测试汇总")
    print(f"  - {detail_file}: 所有原始测试数据（按SN排序）")
    print(f"  - {stats_file}: 整体数据统计")
    print(f"  - 各测试项目的详细统计文件")

    return summary_file

if __name__ == "__main__":
    try:
        output_file = process_test_data()
        print(f"\n处理完成！输出文件: {output_file}")
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
