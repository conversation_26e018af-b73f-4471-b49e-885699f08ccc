import csv
import json
from datetime import datetime

def create_excel_like_format():
    """
    创建类似Excel的格式化输出，使用制表符分隔
    """
    
    # 读取汇总数据
    summary_file = '设备测试汇总_20250902_163019.csv'
    
    # 创建Excel格式的文本文件
    excel_file = f'测试数据分析_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    
    with open(excel_file, 'w', encoding='utf-8-sig') as f:
        f.write("=" * 80 + "\n")
        f.write("设备测试数据分析报告\n")
        f.write("=" * 80 + "\n\n")
        
        # 统计信息
        f.write("【统计信息】\n")
        f.write("-" * 40 + "\n")
        with open('统计信息_20250902_163019.csv', 'r', encoding='utf-8-sig') as stats_f:
            reader = csv.DictReader(stats_f)
            for row in reader:
                f.write(f"{row['统计项目']}: {row['数值']}\n")
        f.write("\n")
        
        # 设备汇总表
        f.write("【设备测试汇总表】\n")
        f.write("-" * 40 + "\n")
        
        with open(summary_file, 'r', encoding='utf-8-sig') as csv_f:
            reader = csv.DictReader(csv_f)
            
            # 写入表头
            headers = [
                'SN', 'TestID', 'FirstTestTime', 'LastTestTime', 'TestCount',
                'AD_V15电压_值', 'AD_V15电压_合格',
                'AD_V16电压_值', 'AD_V16电压_合格', 
                '转刹把供电电压_值', '转刹把供电电压_合格',
                '左刹电压_值', '左刹电压_合格',
                '右刹电压_值', '右刹电压_合格',
                '转把电压_值', '转把电压_合格'
            ]
            
            f.write("\t".join(headers) + "\n")
            f.write("-" * 200 + "\n")
            
            # 写入数据行
            for i, row in enumerate(reader):
                if i >= 50:  # 只显示前50行作为示例
                    f.write(f"... (共1000个设备，仅显示前50个)\n")
                    break
                    
                data_row = [
                    row['SN'],
                    row['TestID'][:8] + "...",  # 缩短TestID显示
                    row['FirstTestTime'],
                    row['LastTestTime'], 
                    row['TestCount'],
                    row['读取AD_V15电压(前)_值'],
                    row['读取AD_V15电压(前)_合格'],
                    row['读取AD_V16电压(前)_值'],
                    row['读取AD_V16电压(前)_合格'],
                    row['转刹把供电电压(主控ADC)_值'],
                    row['转刹把供电电压(主控ADC)_合格'],
                    row['左刹电压测试1_值'],
                    row['左刹电压测试1_合格'],
                    row['右刹电压测试1_值'],
                    row['右刹电压测试1_合格'],
                    row['转把电压测试1_值'],
                    row['转把电压测试1_合格']
                ]
                
                f.write("\t".join(data_row) + "\n")
        
        f.write("\n\n")
        
        # 测试项目详细分析
        f.write("【测试项目详细分析】\n")
        f.write("-" * 40 + "\n")
        
        test_files = [
            ('读取AD_V15电压前_20250902_163019.csv', 'AD_V15电压测试'),
            ('读取AD_V16电压前_20250902_163019.csv', 'AD_V16电压测试'),
            ('转刹把供电电压主控ADC_20250902_163019.csv', '转刹把供电电压测试'),
            ('左刹电压测试1_20250902_163019.csv', '左刹电压测试'),
            ('右刹电压测试1_20250902_163019.csv', '右刹电压测试'),
            ('转把电压测试1_20250902_163019.csv', '转把电压测试')
        ]
        
        for file_name, test_name in test_files:
            f.write(f"\n{test_name}:\n")
            
            try:
                with open(file_name, 'r', encoding='utf-8-sig') as test_f:
                    reader = csv.DictReader(test_f)
                    
                    # 统计合格率
                    total_count = 0
                    pass_count = 0
                    values = []
                    
                    for row in reader:
                        total_count += 1
                        if row['合格状态'] == '合格':
                            pass_count += 1
                        values.append(float(row['val']))
                    
                    pass_rate = (pass_count / total_count * 100) if total_count > 0 else 0
                    avg_value = sum(values) / len(values) if values else 0
                    min_value = min(values) if values else 0
                    max_value = max(values) if values else 0
                    
                    f.write(f"  测试数量: {total_count}\n")
                    f.write(f"  合格数量: {pass_count}\n")
                    f.write(f"  合格率: {pass_rate:.1f}%\n")
                    f.write(f"  平均值: {avg_value:.1f}\n")
                    f.write(f"  最小值: {min_value}\n")
                    f.write(f"  最大值: {max_value}\n")
                    
            except FileNotFoundError:
                f.write(f"  文件未找到: {file_name}\n")
    
    print(f"Excel格式报告已生成: {excel_file}")
    return excel_file

if __name__ == "__main__":
    try:
        output_file = create_excel_like_format()
        print(f"处理完成！输出文件: {output_file}")
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
